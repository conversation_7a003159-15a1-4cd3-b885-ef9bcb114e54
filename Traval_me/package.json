{"name": "traval-me", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.2.0", "@material-tailwind/react": "^2.1.10", "@tailwindcss/vite": "^4.1.13", "autoprefixer": "^10.4.21", "framer-motion": "^12.23.21", "lucide-react": "^0.544.0", "postcss": "^8.5.6", "react": "^19.1.1", "react-dom": "^19.1.1", "react-icons": "^5.5.0", "react-router-dom": "^7.9.1", "tailwindcss": "^4.1.13"}, "devDependencies": {"@eslint/js": "^9.35.0", "@types/react": "^19.1.13", "@types/react-dom": "^19.1.9", "@vitejs/plugin-react": "^5.0.2", "eslint": "^9.35.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.4.0", "vite": "^7.1.6"}}